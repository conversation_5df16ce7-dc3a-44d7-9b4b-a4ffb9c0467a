let currentPin = '';
let transactionData = { name: 'Dad', amount: '1' };

function showScreen(screenId) {
    document.querySelectorAll('.screen').forEach(screen => {
        screen.classList.remove('active');
    });
    document.getElementById(screenId).classList.add('active');
}

function proceedToPin() {
    const name = document.getElementById('recipientName').value;
    const amount = document.getElementById('amount').value;
    
    if (!name || !amount) {
        alert('Please enter both name and amount');
        return;
    }
    
    transactionData = { name, amount };
    document.getElementById('transactionInfo').textContent = `Sending ₹${amount} to ${name}`;
    showScreen('pinScreen');
}

function addPin(digit) {
    if (currentPin.length < 6) {
        currentPin += digit;
        updatePinDots();
        
        // Auto-submit when 6 digits are entered
        if (currentPin.length === 6) {
            setTimeout(() => {
                submitPin();
            }, 300);
        }
    }
}

function clearPin() {
    if (currentPin.length > 0) {
        currentPin = currentPin.slice(0, -1);
        updatePinDots();
    }
}

function updatePinDots() {
    const dots = document.querySelectorAll('.dot');
    dots.forEach((dot, index) => {
        if (index < currentPin.length) {
            dot.classList.add('filled');
        } else {
            dot.classList.remove('filled');
        }
    });
}

function submitPin() {
    if (currentPin.length === 6) {
        // Simulate processing
        setTimeout(() => {
            showSuccessScreen();
        }, 1000);
    } else {
        alert('Please enter complete 6-digit PIN');
    }
}

function showSuccessScreen() {
    const now = new Date();
    const dateStr = `${now.getDate()} July ${now.getFullYear()} at ${formatTime(now)}`;
    
    document.getElementById('successDate').textContent = dateStr;
    document.getElementById('successName').textContent = transactionData.name;
    document.getElementById('successAmount').textContent = `₹${transactionData.amount}`;
    
    showScreen('successScreen');
}

function formatTime(date) {
    let hours = date.getHours();
    const minutes = date.getMinutes();
    const ampm = hours >= 12 ? 'PM' : 'AM';
    
    hours = hours % 12;
    hours = hours ? hours : 12; // the hour '0' should be '12'
    
    const minutesStr = minutes < 10 ? '0' + minutes : minutes;
    
    return `${hours}:${minutesStr} ${ampm}`;
}

function viewDetails() {
    const now = new Date();
    const dateStr = `${formatTime(now)} on ${now.getDate()} Jul ${now.getFullYear()}`;
    
    document.getElementById('detailsDate').textContent = dateStr;
    document.getElementById('detailsName').textContent = transactionData.name;
    document.getElementById('detailsAmount').textContent = `₹${transactionData.amount}`;
    
    showScreen('detailsScreen');
}

function shareReceipt() {
    const now = new Date();
    const day = now.getDate().toString().padStart(2, '0');
    const month = (now.getMonth() + 1).toString().padStart(2, '0');
    const year = now.getFullYear();
    const dateStr = `${day}/${month}/${year} ${formatTime(now)}`;
    
    document.getElementById('receiptName').textContent = transactionData.name;
    document.getElementById('receiptAmount').textContent = `₹${transactionData.amount}`;
    document.getElementById('receiptDate').textContent = dateStr;
    
    showScreen('receiptScreen');
}

function backToSuccess() {
    showScreen('successScreen');
}

function closeReceipt() {
    showScreen('successScreen');
}

function goHome() {
    // Reset form
    document.getElementById('recipientName').value = '';
    document.getElementById('amount').value = '';
    currentPin = '';
    updatePinDots();
    showScreen('homeScreen');
}

function downloadReceipt() {
    alert('Receipt downloaded successfully!');
}

function shareReceiptExternal() {
    if (navigator.share) {
        navigator.share({
            title: 'Payment Receipt',
            text: `Payment of ₹${transactionData.amount} sent to ${transactionData.name}`,
        });
    } else {
        alert('Receipt shared successfully!');
    }
}

function sendAgain() {
    document.getElementById('recipientName').value = transactionData.name;
    document.getElementById('amount').value = transactionData.amount;
    showScreen('homeScreen');
}

function viewHistory() {
    alert('Transaction history feature coming soon!');
}

function splitExpense() {
    alert('Split expense feature coming soon!');
}

function contactSupport() {
    alert('Contacting PhonePe Support...');
}

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    showScreen('homeScreen');
});

