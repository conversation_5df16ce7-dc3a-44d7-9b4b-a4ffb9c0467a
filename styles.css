* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: #f5f5f5;
    overflow-x: hidden;
}

.status-bar {
    background: #333;
    color: white;
    padding: 5px 10px;
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
}

.screen {
    display: none;
    min-height: 100vh;
    padding-top: 30px;
    background-color: #f5f5f5;
}

.screen.active {
    display: block;
}

/* Home Screen */
.container {
    padding: 40px 20px;
    max-width: 400px;
    margin: 0 auto;
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.container h2 {
    text-align: left;
    margin-bottom: 30px;
    color: #333;
    font-size: 20px;
    font-weight: 600;
}

.form-group {
    margin-bottom: 20px;
}

.input-field {
    width: 100%;
    padding: 12px 0;
    border: none;
    border-bottom: 1px solid #ddd;
    background: transparent;
    font-size: 16px;
    outline: none;
    transition: border-color 0.3s;
}

.input-field:focus {
    border-bottom-color: #2196F3;
}

.submit-btn {
    width: 100%;
    padding: 12px;
    background: #2196F3;
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    margin-top: 30px;
}

/* PIN Screen */
.pin-container {
    padding: 20px;
    text-align: center;
    max-width: 400px;
    margin: 0 auto;
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.pin-header {
    margin-bottom: 20px;
}

.pin-header h3 {
    margin-bottom: 10px;
    color: #333;
    font-size: 18px;
    font-weight: 600;
}

.pin-header p {
    color: #666;
    font-size: 14px;
}

.pin-dots {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin: 30px 0;
}

.dot {
    width: 12px;
    height: 12px;
    border: 1px solid #999;
    border-radius: 50%;
    transition: all 0.3s;
}

.dot.filled {
    background: #2196F3;
    border-color: #2196F3;
}

.keypad {
    max-width: 280px;
    margin: 0 auto;
}

.keypad-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
}

.keypad button {
    width: 60px;
    height: 60px;
    border: none;
    border-radius: 50%;
    background: #f8f8f8;
    font-size: 22px;
    cursor: pointer;
    transition: all 0.2s;
}

.keypad button:hover {
    background: #e0e0e0;
}

.back-btn {
    background: #e0e0e0 !important;
    color: #333;
}

.submit-pin {
    background: #2196F3 !important;
    color: white;
}

/* Success Screen */
.success-header {
    background: #4CAF50;
    color: white;
    text-align: center;
    padding: 30px 20px;
}

.success-icon {
    width: 60px;
    height: 60px;
    background: white;
    color: #4CAF50;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 30px;
    margin: 0 auto 15px;
}

.success-card {
    background: white;
    margin: 15px;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.recipient-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.avatar {
    width: 45px;
    height: 45px;
    background: #5f27cd;
    color: white;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
}

.recipient-details h3 {
    margin-bottom: 3px;
    font-size: 16px;
}

.recipient-details p {
    color: #666;
    font-size: 12px;
}

.amount-info {
    margin-left: auto;
    text-align: right;
}

.amount-info span {
    font-size: 18px;
    font-weight: bold;
    display: block;
    margin-bottom: 5px;
}

.split-expense {
    background: #673AB7;
    color: white;
    border: none;
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 11px;
}

.action-buttons {
    display: flex;
    justify-content: space-around;
    padding: 15px;
    gap: 10px;
}

.action-btn {
    flex: 1;
    padding: 12px 8px;
    background: #f0f0f0;
    border: none;
    border-radius: 20px;
    text-align: center;
    cursor: pointer;
    font-size: 13px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
}

.ad-banner {
    margin: 15px;
    text-align: center;
}

.ad-banner img {
    width: 100%;
    border-radius: 8px;
}

.done-btn {
    width: calc(100% - 30px);
    margin: 0 15px 15px;
    padding: 12px;
    background: #673AB7;
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 16px;
    font-weight: 500;
}

/* Details Screen */
.header {
    background: #4CAF50;
    color: white;
    padding: 15px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.header .back-btn {
    background: none;
    border: none;
    color: white;
    font-size: 20px;
    cursor: pointer;
}

.header-title h2 {
    font-size: 16px;
    margin-bottom: 3px;
}

.header-title p {
    font-size: 12px;
    opacity: 0.9;
}

.details-content {
    padding: 15px;
    background: #f5f5f5;
}

.paid-to, .sent-to, .transfer-details {
    margin-bottom: 20px;
}

.paid-to h3, .transfer-details h3 {
    font-size: 14px;
    margin-bottom: 10px;
    color: #333;
}

.recipient-card {
    display: flex;
    align-items: center;
    gap: 12px;
    background: white;
    padding: 12px;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.avatar.purple {
    background: #673AB7;
    border-radius: 8px;
}

.recipient-info h4 {
    font-size: 14px;
    margin-bottom: 2px;
}

.recipient-info p {
    font-size: 12px;
    color: #666;
}

.amount {
    font-weight: bold;
    font-size: 16px;
}

.sent-to {
    background: white;
    padding: 12px;
    border-radius: 8px;
    font-size: 13px;
    line-height: 1.5;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    padding: 10px;
    background: white;
    margin-bottom: 1px;
    font-size: 13px;
}

.action-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
}

.action-grid button {
    padding: 10px;
    background: white;
    border: none;
    border-radius: 8px;
    text-align: center;
    cursor: pointer;
    font-size: 12px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
}

.support button {
    width: 100%;
    padding: 12px;
    background: white;
    border: none;
    border-radius: 8px;
    text-align: left;
    cursor: pointer;
    margin: 15px 0;
    font-size: 13px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.powered-by {
    text-align: center;
    margin-top: 20px;
    color: #666;
    font-size: 12px;
}

.bank-logos {
    margin-top: 5px;
    font-weight: 500;
}

/* Receipt Screen */
.receipt {
    background: white;
    margin: 15px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    overflow: hidden;
}

.receipt-header {
    background: #673AB7;
    color: white;
    padding: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.receipt-header h2 {
    font-size: 16px;
}

.close-btn {
    background: none;
    border: none;
    color: white;
    font-size: 20px;
    cursor: pointer;
}

.receipt-content {
    padding: 15px;
}

.receipt-item {
    display: flex;
    justify-content: space-between;
    padding: 10px 0;
    border-bottom: 1px solid #eee;
    font-size: 14px;
}

.receipt-item:last-child {
    border-bottom: none;
}

.success {
    color: #4CAF50;
    font-weight: bold;
}

.receipt-actions {
    padding: 15px;
    display: flex;
    gap: 10px;
}

.receipt-actions button {
    flex: 1;
    padding: 10px;
    border: 1px solid #673AB7;
    background: white;
    color: #673AB7;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
}

.receipt-actions button:first-child {
    background: #673AB7;
    color: white;
}

/* Desktop Responsive */
@media (min-width: 768px) {
    body {
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 100vh;
        background: #f0f0f0;
    }
    
    .screen {
        max-width: 380px;
        background: white;
        border-radius: 10px;
        box-shadow: 0 5px 20px rgba(0,0,0,0.15);
        overflow: hidden;
        margin: 20px;
        min-height: 600px;
        height: auto;
    }
    
    .status-bar {
        position: relative;
        border-radius: 10px 10px 0 0;
    }
}
