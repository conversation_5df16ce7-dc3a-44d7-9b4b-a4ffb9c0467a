<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PhonePe Clone</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <!-- Status Bar -->
    <div class="status-bar">
        <div class="status-left">
            <span class="signal">●●● 2:25</span>
        </div>
        <div class="status-right">
            <span class="battery">📶 🔋 100%</span>
        </div>
    </div>

    <!-- Home Screen -->
    <div id="homeScreen" class="screen active">
        <div class="container">
            <h2>Send Money</h2>
            <div class="form-group">
                <input type="text" id="recipientName" placeholder="Name" class="input-field">
            </div>
            <div class="form-group">
                <input type="number" id="amount" placeholder="Amount" class="input-field">
            </div>
            <button onclick="proceedToPin()" class="submit-btn">Submit</button>
        </div>
    </div>

    <!-- PIN Entry Screen -->
    <div id="pinScreen" class="screen">
        <div class="pin-container">
            <div class="pin-header">
                <h3>Enter 6 Digit UPI PIN</h3>
                <p id="transactionInfo"></p>
            </div>
            <div class="pin-dots">
                <span class="dot"></span>
                <span class="dot"></span>
                <span class="dot"></span>
                <span class="dot"></span>
                <span class="dot"></span>
                <span class="dot"></span>
            </div>
            <div class="keypad">
                <div class="keypad-row">
                    <button onclick="addPin('1')">1</button>
                    <button onclick="addPin('2')">2</button>
                    <button onclick="addPin('3')">3</button>
                </div>
                <div class="keypad-row">
                    <button onclick="addPin('4')">4</button>
                    <button onclick="addPin('5')">5</button>
                    <button onclick="addPin('6')">6</button>
                </div>
                <div class="keypad-row">
                    <button onclick="addPin('7')">7</button>
                    <button onclick="addPin('8')">8</button>
                    <button onclick="addPin('9')">9</button>
                </div>
                <div class="keypad-row">
                    <button onclick="clearPin()" class="back-btn">←</button>
                    <button onclick="addPin('0')">0</button>
                    <button onclick="submitPin()" class="submit-pin">✓</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Success Screen -->
    <div id="successScreen" class="screen">
        <div class="success-header">
            <div class="success-icon">✓</div>
            <h2>Payment Successful</h2>
            <p id="successDate">25 July 2025 at 02:25 PM</p>
        </div>
        <div class="success-card">
            <div class="recipient-info">
                <div class="avatar">👤</div>
                <div class="recipient-details">
                    <h3 id="successName">Dad</h3>
                    <p>XXXXXX1716</p>
                </div>
                <div class="amount-info">
                    <span id="successAmount">₹1</span>
                    <button class="split-expense">Split Expense</button>
                </div>
            </div>
        </div>
        <div class="action-buttons">
            <button onclick="viewDetails()" class="action-btn">
                📄 View Details
            </button>
            <button onclick="shareReceipt()" class="action-btn">
                📤 Share Receipt
            </button>
        </div>
        <div class="ad-banner">
            <img src="https://via.placeholder.com/400x150/FFD700/000000?text=Abhibus+Offer" alt="Ad">
        </div>
        <button onclick="goHome()" class="done-btn">Done</button>
    </div>

    <!-- Transaction Details Screen -->
    <div id="detailsScreen" class="screen">
        <div class="header">
            <button onclick="backToSuccess()" class="back-btn">←</button>
            <div class="header-title">
                <h2>Transaction Successful</h2>
                <p id="detailsDate">02:25 PM on 25 Jul 2025</p>
            </div>
        </div>
        <div class="details-content">
            <div class="paid-to">
                <h3>Paid to</h3>
                <div class="recipient-card">
                    <div class="avatar purple">👤</div>
                    <div class="recipient-info">
                        <h4 id="detailsName">Dad</h4>
                        <p>katakamvenkateswarao2016@okhdfcbank</p>
                    </div>
                    <span id="detailsAmount" class="amount">₹1</span>
                </div>
            </div>
            
            <div class="sent-to">
                <p><strong>Sent to:</strong> 📱 GPay</p>
                <p>katakamvenkateswarao2016@okhdfcbank</p>
            </div>

            <div class="transfer-details">
                <h3>📋 Transfer Details</h3>
                <div class="detail-item">
                    <span>Transaction ID</span>
                    <span>T250725142418899960436 📋</span>
                </div>
                <div class="detail-item">
                    <span>Debited from</span>
                    <div>
                        <p>🏦 XXXXXX6344 ₹1</p>
                        <p>UTR: ************ 📋</p>
                    </div>
                </div>
            </div>

            <div class="bottom-actions">
                <div class="action-grid">
                    <button onclick="sendAgain()">↗️<br>Send Again</button>
                    <button onclick="viewHistory()">🕐<br>View History</button>
                    <button onclick="splitExpense()">⚡<br>Split Expense</button>
                    <button onclick="shareReceipt()">📤<br>Share Receipt</button>
                </div>
            </div>

            <div class="support">
                <button onclick="contactSupport()">❓ Contact PhonePe Support <span>></span></button>
            </div>

            <div class="powered-by">
                <p>Powered by</p>
                <div class="bank-logos">UPI | AXIS BANK</div>
            </div>
        </div>
    </div>

    <!-- Receipt Screen -->
    <div id="receiptScreen" class="screen">
        <div class="receipt">
            <div class="receipt-header">
                <h2>Payment Receipt</h2>
                <button onclick="closeReceipt()" class="close-btn">×</button>
            </div>
            <div class="receipt-content">
                <div class="receipt-item">
                    <span>To:</span>
                    <span id="receiptName">Dad</span>
                </div>
                <div class="receipt-item">
                    <span>Amount:</span>
                    <span id="receiptAmount">₹1</span>
                </div>
                <div class="receipt-item">
                    <span>Date:</span>
                    <span id="receiptDate">25/07/2025 02:25 PM</span>
                </div>
                <div class="receipt-item">
                    <span>Transaction ID:</span>
                    <span>T250725142418899960436</span>
                </div>
                <div class="receipt-item">
                    <span>Status:</span>
                    <span class="success">Successful</span>
                </div>
            </div>
            <div class="receipt-actions">
                <button onclick="downloadReceipt()">Download</button>
                <button onclick="shareReceiptExternal()">Share</button>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>

